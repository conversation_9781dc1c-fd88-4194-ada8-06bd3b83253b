# Restaurant Directory Mobile App Design

## Overview

A modern, trendy mobile-first restaurant and hotel directory application built with HTML, Tailwind CSS, and custom CSS featuring subtle animations. The app serves as a multi-sided marketplace connecting customers, vendors (restaurants/hotels), and food bloggers through a commission-based referral system.

### Target Users
- **Customers**: Users searching for restaurants, viewing reviews, claiming coupons
- **Vendors**: Restaurant/hotel owners listing their businesses, managing offers and menus
- **Food Bloggers**: Content creators earning commissions through referrals

## Technology Stack & Dependencies

### Frontend Technologies
- **HTML5**: Semantic markup structure
- **Tailwind CSS**: Utility-first CSS framework for rapid styling
- **Custom CSS**: Brand-specific styling and advanced animations
- **JavaScript**: Interactive functionality and animations
- **CSS Animations**: Subtle micro-interactions and transitions

### Design Tools
- **Android Mockup Device**: Design target for mobile-first approach
- **Responsive Design**: Optimized for mobile screens (Android focus)

## Zomato-Inspired Color Palette System

### Primary Palette: Zomato Classic Red
```css
--primary: #E23744        /* Zomato Red */
--secondary: #FF6B35      /* Vibrant Orange */
--accent: #FFD700         /* Golden Yellow */
--neutral: #FFFFFF        /* Pure White */
--text: #1C1C1C           /* Deep Black */
```

### Palette 2: Zomato Orange Focus
```css
--primary: #FF6B35        /* Zomato Orange */
--secondary: #E23744      /* Zomato Red */
--accent: #FFC107         /* Bright Yellow */
--neutral: #FFF8F0        /* Warm White */
--text: #2D2D2D           /* Charcoal */
```

### Palette 3: Zomato Yellow Energy
```css
--primary: #FFD700        /* Zomato Yellow */
--secondary: #FF6B35      /* Zomato Orange */
--accent: #E23744         /* Zomato Red */
--neutral: #FFFEF7        /* Light Cream */
--text: #333333           /* Dark Gray */
```

### Palette 4: Zomato Dark Mode
```css
--primary: #E23744        /* Zomato Red */
--secondary: #FF8C42      /* Light Orange */
--accent: #FFE55C         /* Soft Yellow */
--neutral: #1A1A1A        /* Dark Background */
--text: #FFFFFF           /* White Text */
```

### Palette 5: Zomato Gradient Fusion
```css
--primary: #E23744        /* Zomato Red */
--secondary: #FF6B35      /* Zomato Orange */
--accent: #FFD700         /* Zomato Yellow */
--neutral: #F9F9F9        /* Light Gray */
--text: #2C2C2C           /* Almost Black */
```

## Component Architecture

### Component Hierarchy

```
App
├── Authentication
│   ├── LoginComponent
│   ├── RegisterComponent
│   └── UserTypeSelector
├── Navigation
│   ├── BottomNavigation
│   ├── SideDrawer
│   └── HeaderWithSearch
├── Customer Components
│   ├── SearchInterface
│   ├── RestaurantListing
│   ├── RestaurantDetail
│   ├── ReviewSystem
│   └── CouponSection
├── Vendor Components
│   ├── VendorDashboard
│   ├── ListingManagement
│   ├── MenuManager
│   └── OfferCreator
├── Blogger Components
│   ├── BloggerDashboard
│   ├── ReferralTracker
│   └── CommissionPortal
└── Shared Components
    ├── LoadingAnimations
    ├── ButtonComponents
    ├── CardComponents
    └── FormComponents
```

### Component Definitions

#### Authentication Components
**LoginComponent**
- Purpose: User authentication interface
- Props: `userType`, `onSuccess`, `redirectPath`
- State: `email`, `password`, `isLoading`, `errors`
- Animations: Smooth form transitions, loading spinners

**RegisterComponent**
- Purpose: New user registration with role selection
- Props: `selectedUserType`, `onRegistrationComplete`
- State: `formData`, `validationErrors`, `step`
- Features: Multi-step registration, role-based form fields

#### Customer Experience Components
**SearchInterface**
- Purpose: Location-based restaurant search
- Props: `currentLocation`, `searchRadius`, `onSearchResults`
- State: `searchQuery`, `filters`, `suggestions`
- Animations: Search bar expansion, filter slide-ins

**RestaurantListing**
- Purpose: Display search results with key information
- Props: `restaurants`, `currentLocation`, `onItemSelect`
- Features: Card-based layout, distance calculation, rating display
- Animations: Staggered card animations, hover effects

**RestaurantDetail**
- Purpose: Comprehensive restaurant information view
- Props: `restaurantId`, `userLocation`
- Components: Image gallery, reviews, menu preview, offers
- Animations: Image carousel, review scroll animations

#### Vendor Management Components
**VendorDashboard**
- Purpose: Central control panel for restaurant owners
- Features: Analytics overview, quick actions, notification center
- Metrics: Views, bookings, revenue, ratings

**ListingManagement**
- Purpose: Create and edit restaurant listings
- Features: Photo upload, business information, location mapping
- Validation: Required fields, image optimization, location verification

#### Blogger Components
**BloggerDashboard**
- Purpose: Track referrals and commission earnings
- Features: Performance metrics, payment history, referral links
- Analytics: Click-through rates, conversion tracking

## Styling Strategy

### Zomato-Inspired Tailwind CSS Utilities
```css
/* Layout Classes with Zomato Styling */
.container-mobile { @apply max-w-sm mx-auto px-4; }
.card-zomato { @apply bg-white rounded-xl shadow-lg p-4 border border-gray-100; }
.button-zomato-primary { @apply bg-red-600 text-white py-3 px-6 rounded-full font-semibold; }
.button-zomato-secondary { @apply bg-orange-500 text-white py-2 px-5 rounded-full; }

/* Zomato Brand Colors */
.bg-zomato-red { background-color: #E23744; }
.bg-zomato-orange { background-color: #FF6B35; }
.bg-zomato-yellow { background-color: #FFD700; }
.text-zomato-red { color: #E23744; }
.text-zomato-orange { color: #FF6B35; }
.text-zomato-yellow { color: #FFD700; }

/* Animation Classes */
.fade-in { @apply transition-opacity duration-300 ease-in-out; }
.slide-up { @apply transform translate-y-4 transition-transform duration-300; }
.scale-hover { @apply transition-transform hover:scale-105; }
.zomato-gradient { background: linear-gradient(135deg, #E23744 0%, #FF6B35 50%, #FFD700 100%); }
```

### Zomato-Inspired Custom CSS Animations
```css
/* Zomato Brand Micro-interactions */
@keyframes zomato-pulse {
  0%, 100% { 
    box-shadow: 0 0 0 0 rgba(226, 55, 68, 0.4);
    background-color: #E23744;
  }
  50% { 
    box-shadow: 0 0 0 10px rgba(226, 55, 68, 0);
    background-color: #FF6B35;
  }
}

@keyframes zomato-gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes slide-in-zomato {
  from { 
    transform: translateY(30px) scale(0.95); 
    opacity: 0;
  }
  to { 
    transform: translateY(0) scale(1); 
    opacity: 1;
  }
}

/* Zomato Loading States */
.zomato-shimmer {
  background: linear-gradient(
    90deg,
    #f0f0f0 25%,
    #E23744 50%,
    #FF6B35 75%,
    #f0f0f0 100%
  );
  background-size: 400% 100%;
  animation: zomato-gradient-shift 2s infinite;
}

/* Zomato Button Interactions */
.btn-zomato {
  background: linear-gradient(45deg, #E23744, #FF6B35);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-zomato::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-zomato:hover::before {
  left: 100%;
}

.btn-zomato:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(226, 55, 68, 0.3);
}

/* Restaurant Card Zomato Styling */
.restaurant-card-zomato {
  background: white;
  border-radius: 16px;
  transition: all 0.3s ease;
  border: 1px solid rgba(226, 55, 68, 0.1);
  position: relative;
  overflow: hidden;
}

.restaurant-card-zomato::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #E23744, #FF6B35, #FFD700);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.restaurant-card-zomato:hover::before {
  transform: scaleX(1);
}

.restaurant-card-zomato:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(226, 55, 68, 0.2);
}
```

## Page Structure & User Flows

### Customer Flow Pages

#### 1. Welcome/Onboarding
```mermaid
graph TD
    A[Splash Screen] --> B[User Type Selection]
    B --> C{First Time User?}
    C -->|Yes| D[Onboarding Slides]
    C -->|No| E[Login Screen]
    D --> F[Permission Requests]
    F --> G[Location Access]
    G --> H[Home Dashboard]
```

#### 2. Authentication Flow
- **Login Page**: Email/password with social login options
- **Registration Page**: Multi-step form with role selection
- **Password Recovery**: Email verification flow

#### 3. Customer Home Dashboard
- **Header**: Search bar with location indicator
- **Categories**: Horizontal scroll of cuisine types
- **Featured**: Promoted restaurants and offers
- **Near You**: Location-based recommendations
- **Recent**: Previously visited places

#### 4. Search & Discovery
- **Search Results**: Filterable restaurant listings
- **Map View**: Interactive map with restaurant pins
- **Filter Panel**: Cuisine, price range, ratings, distance
- **Sort Options**: Distance, rating, price, popularity

#### 5. Restaurant Detail View
- **Image Gallery**: Swipeable restaurant photos
- **Basic Info**: Name, rating, cuisine, location
- **Menu Preview**: Featured dishes with prices
- **Reviews Section**: User ratings and comments
- **Offers Panel**: Available coupons and deals
- **Action Buttons**: Call, directions, bookmark

### Vendor Flow Pages

#### 1. Vendor Registration
- **Business Verification**: Document upload and verification
- **Listing Creation**: Restaurant details and photos
- **Menu Setup**: Add dishes, categories, and pricing
- **Package Selection**: Choose subscription plan

#### 2. Vendor Dashboard
```mermaid
graph LR
    A[Dashboard] --> B[Analytics]
    A --> C[Listings]
    A --> D[Menu Management]
    A --> E[Offers & Coupons]
    A --> F[Reviews]
    A --> G[Package Management]
```

#### 3. Listing Management
- **Basic Information**: Name, description, contact details
- **Location & Hours**: Address, operating hours, delivery zones
- **Photos & Media**: Restaurant and food photography
- **Amenities**: Features like parking, WiFi, outdoor seating

#### 4. Menu Management
- **Categories**: Organize dishes by type
- **Items**: Add/edit dishes with photos and descriptions
- **Pricing**: Set prices and mark specials
- **Availability**: Toggle items on/off

#### 5. Offers & Promotions
- **Coupon Creator**: Design discount coupons
- **Flash Sales**: Time-limited offers
- **Loyalty Programs**: Repeat customer incentives
- **Blogger Partnerships**: Referral commission settings

### Blogger Flow Pages

#### 1. Blogger Registration
- **Profile Setup**: Bio, social media links, content focus
- **Portfolio**: Previous work and audience metrics
- **Verification**: Social media account verification

#### 2. Blogger Dashboard
- **Performance Metrics**: Clicks, conversions, earnings
- **Referral Links**: Generate trackable links for restaurants
- **Commission Tracker**: Real-time earnings tracking
- **Payment History**: Monthly commission payments

#### 3. Content Tools
- **Restaurant Discovery**: Browse and select restaurants to promote
- **Media Kit**: Access to restaurant photos and information
- **Tracking Links**: Generate unique referral codes

## State Management Patterns

### Local State Management
```javascript
// Component-level state for UI interactions
const [isLoading, setIsLoading] = useState(false);
const [searchQuery, setSearchQuery] = useState('');
const [selectedFilters, setSelectedFilters] = useState({});
```

### Application State
```javascript
// Global state management structure
const AppState = {
  user: {
    profile: {},
    preferences: {},
    location: {}
  },
  restaurants: {
    list: [],
    filters: {},
    favorites: []
  },
  vendors: {
    dashboard: {},
    listings: [],
    analytics: {}
  },
  bloggers: {
    profile: {},
    referrals: [],
    commissions: {}
  }
};
```

## API Integration Layer

### Data Flow Architecture
```mermaid
graph TB
    A[Frontend Components] --> B[API Service Layer]
    B --> C[Authentication Service]
    B --> D[Restaurant Service]
    B --> E[User Service]
    B --> F[Location Service]
    B --> G[Payment Service]
    
    subgraph "External APIs"
        H[Google Maps API]
        I[Payment Gateway]
        J[SMS/Email Service]
    end
    
    F --> H
    G --> I
    C --> J
```

### API Service Structure
```javascript
// Service layer for API communications
const ApiServices = {
  auth: {
    login: (credentials) => {},
    register: (userData) => {},
    logout: () => {}
  },
  restaurants: {
    search: (location, filters) => {},
    getDetails: (id) => {},
    getReviews: (id) => {},
    getNearby: (coordinates) => {}
  },
  vendor: {
    createListing: (listingData) => {},
    updateMenu: (menuData) => {},
    getAnalytics: () => {}
  },
  blogger: {
    generateReferralLink: (restaurantId) => {},
    trackCommissions: () => {},
    getPaymentHistory: () => {}
  }
};
```

## Zomato-Inspired Animation & Interaction Design

### Zomato Brand Micro-interactions
```css
/* Zomato Primary Button Interactions */
.btn-zomato-primary {
  background: linear-gradient(45deg, #E23744, #FF6B35);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
  box-shadow: 0 4px 14px rgba(226, 55, 68, 0.2);
}

.btn-zomato-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(226, 55, 68, 0.35);
  background: linear-gradient(45deg, #FF6B35, #FFD700);
}

.btn-zomato-primary:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(226, 55, 68, 0.4);
}

/* Zomato Restaurant Card Effects */
.restaurant-card-zomato {
  background: white;
  border-radius: 16px;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 2px solid transparent;
  position: relative;
}

.restaurant-card-zomato::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #E23744, #FF6B35, #FFD700);
  border-radius: 16px 16px 0 0;
  transform: scaleX(0);
  transition: transform 0.4s ease;
}

.restaurant-card-zomato:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(226, 55, 68, 0.15);
  border-color: rgba(226, 55, 68, 0.2);
}

.restaurant-card-zomato:hover::before {
  transform: scaleX(1);
}

/* Zomato Rating Stars Animation */
@keyframes zomato-star-glow {
  0%, 100% { 
    filter: drop-shadow(0 0 2px #FFD700);
    transform: scale(1);
  }
  50% { 
    filter: drop-shadow(0 0 8px #FFD700);
    transform: scale(1.1);
  }
}

.rating-star-zomato {
  color: #FFD700;
  transition: all 0.2s ease;
}

.rating-star-zomato:hover {
  animation: zomato-star-glow 0.6s ease-in-out;
}
```

### Zomato Page Transitions
```css
/* Zomato-style page routing animations */
.page-enter-zomato {
  opacity: 0;
  transform: translateX(30px) scale(0.95);
  filter: blur(2px);
}

.page-enter-active-zomato {
  opacity: 1;
  transform: translateX(0) scale(1);
  filter: blur(0);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.page-exit-zomato {
  opacity: 1;
  transform: translateX(0) scale(1);
  filter: blur(0);
}

.page-exit-active-zomato {
  opacity: 0;
  transform: translateX(-30px) scale(0.95);
  filter: blur(2px);
  transition: all 0.3s cubic-bezier(0.55, 0.085, 0.68, 0.53);
}

/* Zomato Loading Spinner */
@keyframes zomato-spinner {
  0% {
    transform: rotate(0deg);
    border-top-color: #E23744;
  }
  33% {
    border-top-color: #FF6B35;
  }
  66% {
    border-top-color: #FFD700;
  }
  100% {
    transform: rotate(360deg);
    border-top-color: #E23744;
  }
}

.zomato-loading {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #E23744;
  border-radius: 50%;
  animation: zomato-spinner 1s linear infinite;
}
```

## Testing Strategy

### Component Testing
```javascript
// Example test structure for React components
describe('RestaurantCard Component', () => {
  test('renders restaurant information correctly', () => {
    // Test data rendering
  });
  
  test('handles click interactions', () => {
    // Test user interactions
  });
  
  test('displays loading state appropriately', () => {
    // Test loading states
  });
});
```

### User Experience Testing
- **Usability Testing**: Task completion rates for key user flows
- **Performance Testing**: Load times and animation smoothness
- **Accessibility Testing**: Screen reader compatibility and keyboard navigation
- **Mobile Testing**: Touch interactions and responsive design validation

### Cross-Browser Testing
- **Android Browser Compatibility**: Chrome, Samsung Internet, Firefox
- **iOS Safari**: Fallback considerations for iOS users
- **Performance Metrics**: Core Web Vitals optimization

## Business Logic Architecture

### Commission System
```mermaid
graph TD
    A[Blogger Creates Referral] --> B[Customer Clicks Link]
    B --> C[Customer Visits Restaurant]
    C --> D{Vendor Purchases Package?}
    D -->|Yes| E[Commission Calculated]
    D -->|No| F[Track for Future]
    E --> G[Commission Added to Blogger Account]
    G --> H[Monthly Payout Processing]
```

### User Role Management
```javascript
const UserRoles = {
  CUSTOMER: {
    permissions: ['search', 'review', 'claim_coupons'],
    dashboardType: 'customer'
  },
  VENDOR: {
    permissions: ['manage_listing', 'create_offers', 'view_analytics'],
    dashboardType: 'vendor'
  },
  BLOGGER: {
    permissions: ['create_referrals', 'track_commissions', 'view_analytics'],
    dashboardType: 'blogger'
  }
};
```

### Coupon & Offer System
```javascript
const OfferTypes = {
  PERCENTAGE_DISCOUNT: { type: 'percentage', validation: 'range_1_100' },
  FIXED_AMOUNT: { type: 'fixed', validation: 'positive_number' },
  BUY_ONE_GET_ONE: { type: 'bogo', validation: 'item_selection' },
  FREE_DELIVERY: { type: 'delivery', validation: 'minimum_order' }
};
```