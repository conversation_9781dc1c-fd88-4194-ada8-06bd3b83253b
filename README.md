# Foodie - Restaurant Directory Mobile App

A modern, trendy mobile-first restaurant and hotel directory application built with HTML, Tailwind CSS, and custom CSS featuring Zomato-inspired design and subtle animations.

## 🎯 Overview

Foodie is a comprehensive multi-sided marketplace connecting:
- **Customers**: Users searching for restaurants, viewing reviews, claiming coupons
- **Vendors**: Restaurant/hotel owners listing their businesses, managing offers and menus
- **Food Bloggers**: Content creators earning commissions through referrals

## 🎨 Design Features

### Zomato-Inspired Color Palettes

The app includes 5 carefully crafted color palettes inspired by Zomato's brand:

1. **Palette 1 - Zomato Classic Red**
   - Primary: #E23744 (Zomato Red)
   - Secondary: #FF6B35 (Vibrant Orange)
   - Accent: #FFD700 (Golden Yellow)

2. **Palette 2 - Zomato Orange Focus**
   - Primary: #FF6B35 (Zomato Orange)
   - Secondary: #E23744 (Zomato Red)
   - Accent: #FFC107 (Bright Yellow)

3. **Palette 3 - Zomato Yellow Energy**
   - Primary: #FFD700 (Zomato Yellow)
   - Secondary: #FF6B35 (Zomato Orange)
   - Accent: #E23744 (Zomato Red)

4. **Palette 4 - Zomato Dark Mode**
   - Primary: #E23744 (Zomato Red)
   - Secondary: #FF8C42 (Light Orange)
   - Accent: #FFE55C (Soft Yellow)

5. **Palette 5 - Zomato Gradient Fusion**
   - Primary: #E23744 (Zomato Red)
   - Secondary: #FF6B35 (Zomato Orange)
   - Accent: #FFD700 (Zomato Yellow)

### Mobile-First Design
- Android mockup device frame
- Responsive layout optimized for mobile screens
- Touch-friendly interactions and buttons
- Smooth page transitions and animations

## ✨ Features Implemented

### 🔐 Authentication System
- User type selection (Customer/Vendor/Food Blogger)
- Login and registration forms with validation
- Social login options (Google, Facebook)
- Smooth form transitions and animations

### 👤 Customer Experience
- **Home Dashboard**: Location-based greeting, search bar, categories
- **Restaurant Listings**: Card-based layout with ratings, distance, offers
- **Restaurant Details**: Images, reviews, menu preview, offers
- **Search Interface**: Real-time search with filters
- **Bottom Navigation**: Home, Search, Favorites, Profile

### 🏪 Vendor Features (Framework Ready)
- Vendor dashboard structure
- Listing management interface
- Menu management system
- Offer creation tools

### 📝 Food Blogger Features (Framework Ready)
- Blogger dashboard
- Referral tracking system
- Commission portal

### 🎛️ Shared Components
- **Enhanced Cards**: Elevated, interactive, hover effects
- **Button Variants**: Primary, outline, ghost, floating
- **Loading States**: Spinners, skeleton screens, shimmer effects
- **Form Components**: Floating labels, validation, input groups
- **Toast Notifications**: Success/error messages with animations
- **Rating System**: Interactive star ratings
- **Progress Bars**: Animated progress indicators

## 🛠️ Technology Stack

- **HTML5**: Semantic markup structure
- **Tailwind CSS**: Utility-first CSS framework
- **Custom CSS**: Zomato-inspired animations and brand styling
- **JavaScript**: Interactive functionality and state management
- **Font Awesome**: Icon library for UI elements

## 🚀 Getting Started

1. **Clone or Download** the project files
2. **Open** `index.html` in a modern web browser
3. **Interact** with the color palette switcher in the top-right corner
4. **Navigate** through different user flows:
   - Select user type (Customer/Vendor/Blogger)
   - Try login/registration flows
   - Explore customer dashboard and restaurant details

## 📱 Usage

### Color Palette Switching
- Click the colored dots in the top-right corner to switch between palettes
- Each palette instantly updates the entire app's color scheme
- Perfect for demonstrating brand flexibility

### Navigation Flow
1. **Welcome Screen** → User type selection
2. **Authentication** → Login or register
3. **Customer Dashboard** → Browse restaurants
4. **Restaurant Details** → View details, menu, offers

### Interactive Elements
- **Hover Effects**: Cards and buttons respond to interactions
- **Loading States**: Buttons show loading spinners during actions
- **Toast Notifications**: Success/error messages appear dynamically
- **Smooth Transitions**: Pages slide in/out with animations

## 🎭 Animation Features

### Zomato-Inspired Animations
- **Button Interactions**: Gradient shifts, hover elevations, shimmer effects
- **Card Animations**: Scale transforms, shadow changes, color bar reveals
- **Page Transitions**: Slide animations with blur effects
- **Loading Effects**: Gradient shimmer, pulse animations
- **Micro-interactions**: Rating stars, navigation items, form elements

### CSS Animation Classes
```css
.fade-in              /* Slide in from bottom with scale */
.restaurant-card      /* Hover elevation and color bar */
.btn-zomato          /* Gradient background with shimmer */
.loading-skeleton    /* Shimmer loading effect */
.page-enter-active   /* Smooth page transitions */
```

## 📋 Component Architecture

### Authentication Components
- LoginComponent
- RegisterComponent
- UserTypeSelector
- SocialLoginButtons

### Customer Components
- CustomerDashboard
- SearchInterface
- RestaurantListing
- RestaurantDetail
- CategorySelector
- BottomNavigation

### Shared Components
- ButtonComponents (Primary, Outline, Ghost, Floating)
- CardComponents (Basic, Elevated, Interactive)
- FormComponents (Input, Label, Validation)
- LoadingComponents (Spinner, Skeleton, Shimmer)
- ToastNotifications
- RatingSystem
- ProgressBars

## 🎨 Customization

### Adding New Color Palettes
1. Define CSS custom properties in a new `.palette-X` class
2. Add palette button to the switcher
3. Update the `switchPalette()` function

### Extending Components
1. Follow the existing CSS naming conventions
2. Use CSS custom properties for theming
3. Maintain responsive design principles
4. Add smooth transitions for interactions

## 📊 Performance Features

- **Lazy Loading**: Images loaded as needed
- **Smooth Scrolling**: Optimized for mobile devices
- **Efficient Animations**: GPU-accelerated transforms
- **Minimal JavaScript**: Vanilla JS for better performance
- **CDN Resources**: Tailwind CSS and Font Awesome via CDN

## 🎯 Future Enhancements

### Planned Features
- Vendor dashboard implementation
- Food blogger commission system
- Advanced search filters
- Map integration
- Real-time notifications
- Payment gateway integration
- Review and rating system
- Social sharing features

### Technical Improvements
- Progressive Web App (PWA) capabilities
- Offline functionality
- Push notifications
- API integration
- Database connectivity
- User authentication backend

## 📝 File Structure

```
foodie/
├── index.html              # Main application file
├── README.md              # Project documentation
├── color-templates.html   # Color palette reference
└── .qoder/quests/        # Project planning documents
    └── restaurant-directory-app.md
```

## 🌟 Key Highlights

- **5 Zomato-inspired color palettes** with instant switching
- **Mobile-first responsive design** with Android mockup
- **Comprehensive authentication system** with social login
- **Rich customer experience** with search and restaurant details
- **Advanced animations** and micro-interactions
- **Reusable component library** with consistent styling
- **Toast notification system** for user feedback
- **Loading states and skeleton screens** for better UX
- **Smooth page transitions** with CSS animations

## 📞 Support

This is a frontend demonstration project showcasing modern web development techniques and Zomato-inspired design principles. The project serves as a foundation for building a complete restaurant directory application.

---

**Developed with ❤️ using modern web technologies and Zomato design inspiration**