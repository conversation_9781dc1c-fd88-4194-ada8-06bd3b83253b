<!-- Tailwind CSS CDN -->
<script src="https://cdn.tailwindcss.com"></script>

<!-- Google Fonts: Inter -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">

<!-- Font Awesome CDN -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<!-- Custom CSS -->
<link rel="stylesheet" href="../../../assets/css/color-palettes.css">
<link rel="stylesheet" href="../../../assets/css/components.css">

<!-- Tailwind Configuration -->
<script>
    tailwind.config = {
        theme: {
            extend: {
                colors: {
                    brand: {
                        red: '#E23744',
                        yellow: '#FFC107',
                        dark: '#1E1E1E',
                        bg: '#FFF9F2'
                    }
                }
            }
        }
    }
</script>

<style>
    /* Custom styles */
    body {
        font-family: 'Inter', sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    /* Smooth scrolling */
    html {
        scroll-behavior: smooth;
    }

    /* Custom animations */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fadeInUp {
        animation: fadeInUp 0.6s ease-out;
    }

    /* Button hover effects */
    .btn-primary {
        @apply bg-brand-red text-white px-6 py-3 rounded-full font-semibold hover:bg-red-600 transition-all duration-300 transform hover:scale-105;
    }

    .btn-secondary {
        @apply border-2 border-brand-red text-brand-red px-6 py-3 rounded-full font-semibold hover:bg-brand-red hover:text-white transition-all duration-300;
    }

    /* Card hover effects */
    .card-hover {
        @apply transition-all duration-300 hover:shadow-2xl hover:-translate-y-2;
    }

    /* Mobile optimizations */
    @media (max-width: 768px) {
        .mobile-padding {
            padding-left: 1rem;
            padding-right: 1rem;
        }
    }

    /* Loading animation */
    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }

    .loading {
        animation: spin 1s linear infinite;
    }
</style>