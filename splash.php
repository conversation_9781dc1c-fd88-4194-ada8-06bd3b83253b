<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>SwadSangam Splash</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        brand: {
                            red: '#E23744',
                            yellow: '#FFC107',
                            dark: '#1E1E1E',
                            bg: '#FFF9F2'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        /* Custom styles for animations and font */
        body {
            font-family: 'Inter', sans-serif;
        }

        /* Animation for elements fading in and sliding up */
        @keyframes fadeInSlideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Keyframes for the shimmer effect */
        @keyframes shimmer {
            0% {
                background-position: -200% center;
            }

            100% {
                background-position: 200% center;
            }
        }

        /* Class to apply the shimmer effect */
        .shimmer-text {
            /* The gradient defines the shimmer color and highlight */
            background-image: linear-gradient(to right,
                    #E5E7EB 20%,
                    /* Tailwind gray-200 for a silver look */
                    #FFFFFF 50%,
                    /* Pure white for the highlight */
                    #E5E7EB 80%
                    /* Back to silver */
                );
            background-size: 200% auto;
            /* Large background to allow for movement */
            color: transparent;
            /* Makes the text transparent */
            background-clip: text;
            /* Clips the background to the text shape */
            -webkit-background-clip: text;
            /* For Safari compatibility */
            animation: shimmer 4s linear infinite;
            /* Applies the animation */
        }

        /* Applying animations with delays */
        .animate-fade-in-slide-up {
            animation: fadeInSlideUp 0.8s ease-out forwards;
        }

        .delay-1 {
            animation-delay: 0.2s;
        }

        .delay-2 {
            animation-delay: 0.4s;
        }

        .delay-3 {
            animation-delay: 0.6s;
        }

        /* Hide elements initially for animation */
        .animated {
            opacity: 0;
        }
    </style>
</head>

<body class="bg-brand-red flex flex-col items-center justify-center h-screen text-center px-6 overflow-hidden relative">

    <!-- Center Logo and Content -->
    <div class="flex flex-col items-center space-y-4">

        <!-- Logo -->
        <div class="animated animate-fade-in-slide-up w-20 h-20 flex items-center justify-center rounded-2xl bg-white shadow-lg">
            <span class="text-brand-red font-extrabold text-3xl">SS</span>
        </div>

        <!-- App Name -->
        <h1 class="animated animate-fade-in-slide-up delay-1 text-3xl sm:text-4xl font-extrabold text-white">
            Swad<span class="text-brand-yellow">Sangam</span>
        </h1>

        <!-- Tagline 1 with Shimmer Effect -->
        <p class="animated animate-fade-in-slide-up delay-2 text-sm sm:text-base font-semibold shimmer-text">Discover. Taste. Connect.</p>

    </div>

    <!-- Bottom Text -->
    <p class="animated animate-fade-in-slide-up delay-3 text-xs sm:text-sm text-white/70 absolute bottom-10">Powered by Alyanka</p>

    <!-- Auto-redirect Script -->
    <script>
        // Wait for all animations to complete, then redirect to onboarding
        window.addEventListener('load', () => {
            // Calculate total animation time: 0.8s base + 0.6s delay + 0.5s buffer = 1.9s
            setTimeout(() => {
                window.location.href = 'onboarding.php';
            }, 2500); // 2.5 seconds for a smooth experience
        });

        // Optional: Allow users to skip by clicking anywhere
        document.body.addEventListener('click', () => {
            window.location.href = 'onboarding.php';
        });

        // Optional: Allow users to skip by pressing any key
        document.addEventListener('keydown', () => {
            window.location.href = 'onboarding.php';
        });
    </script>

</body>

</html>