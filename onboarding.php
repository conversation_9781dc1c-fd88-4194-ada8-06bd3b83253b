<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="theme-color" content="#E23744">
    <title>SwadSangam Onboarding</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Font Awesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        brand: {
                            red: '#E23744',
                            yellow: '#FFC107',
                            dark: '#1E1E1E',
                            bg: '#FFF9F2'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        /* Custom styles for animations and font */
        body {
            font-family: 'Inter', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            touch-action: manipulation;
            -webkit-tap-highlight-color: transparent;
            overscroll-behavior: none;
        }

        .screen {
            display: none;
            animation: fadeIn 0.5s ease-in-out;
            transform: translateZ(0);
            backface-visibility: hidden;
        }

        .active {
            display: flex;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px) translateZ(0);
            }

            to {
                opacity: 1;
                transform: translateY(0) translateZ(0);
            }
        }

        /* Custom message box for alerts */
        .message-box {
            position: fixed;
            top: -100px;
            left: 50%;
            transform: translateX(-50%);
            padding: 12px 24px;
            background-color: #2c3e50;
            color: white;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            transition: top 0.5s ease-in-out;
            z-index: 1000;
            font-size: 14px;
            max-width: 90vw;
            text-align: center;
        }

        /* Lottie animation container styles */
        .lottie-container {
            width: 280px;
            height: 280px;
            max-width: 70vw;
            max-height: 35vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
        }

        .lottie-container dotlottie-wc {
            width: 100% !important;
            height: 100% !important;
            object-fit: contain;
        }

        .svg-container {
            width: 280px;
            height: 280px;
            max-width: 70vw;
            max-height: 35vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
        }

        /* Font Awesome icon animation styles */
        .svg-container i {
            font-size: 120px;
            color: #FFC107;
            animation: iconFloat 3s ease-in-out infinite;
            transition: transform 0.3s ease;
        }

        /* SVG animation styles */
        .svg-container svg {
            width: 100%;
            height: 100%;
            max-width: 100%;
            max-height: 100%;
            animation: iconFloat 3s ease-in-out infinite;
        }

        /* Custom image animation styles */
        .svg-container img {
            width: 100%;
            height: 100%;
            max-width: 100%;
            max-height: 100%;
            animation: iconFloat 3s ease-in-out infinite;
            object-fit: contain;
        }

        @keyframes iconFloat {

            0%,
            100% {
                transform: translateY(0px) scale(1);
            }

            50% {
                transform: translateY(-10px) scale(1.05);
            }
        }

        /* Hover effect for Font Awesome icons */
        .svg-container:hover i {
            animation-play-state: paused;
            transform: scale(1.1);
        }

        /* Hover effect for SVG icons */
        .svg-container:hover svg {
            animation-play-state: paused;
            transform: scale(1.1);
            transition: transform 0.3s ease;
        }

        /* Hover effect for custom images */
        .svg-container:hover img {
            animation-play-state: paused;
            transform: scale(1.1);
            transition: transform 0.3s ease;
        }

        /* Mobile optimizations */
        @media (max-width: 768px) {
            .container-mobile {
                padding-left: 1rem;
                padding-right: 1rem;
                padding-top: max(1rem, env(safe-area-inset-top));
                padding-bottom: max(1rem, env(safe-area-inset-bottom));
            }

            .lottie-container {
                width: 240px;
                height: 240px;
                max-width: 60vw;
                max-height: 30vh;
            }

            .svg-container {
                width: 240px;
                height: 240px;
                max-width: 60vw;
                max-height: 30vh;
            }

            .svg-container i {
                font-size: 100px;
            }

            .controls-mobile {
                padding-bottom: max(1rem, env(safe-area-inset-bottom));
            }
        }

        @media (max-width: 480px) {
            .lottie-container {
                width: 200px;
                height: 200px;
                max-width: 55vw;
                max-height: 25vh;
            }

            .svg-container {
                width: 200px;
                height: 200px;
                max-width: 55vw;
                max-height: 25vh;
            }

            .svg-container i {
                font-size: 80px;
            }

            .text-responsive {
                font-size: 1.25rem;
                line-height: 1.75rem;
            }

            .description-responsive {
                font-size: 0.875rem;
                line-height: 1.25rem;
            }
        }

        @media (max-width: 360px) {
            .lottie-container {
                width: 180px;
                height: 180px;
                max-width: 50vw;
                max-height: 22vh;
            }

            .svg-container {
                width: 180px;
                height: 180px;
                max-width: 50vw;
                max-height: 22vh;
            }

            .svg-container i {
                font-size: 70px;
            }
        }

        /* Touch-friendly buttons */
        .touch-button {
            min-height: 44px;
            min-width: 44px;
            touch-action: manipulation;
            -webkit-tap-highlight-color: transparent;
            transition: all 0.2s ease;
        }

        .touch-button:active {
            transform: scale(0.95);
        }

        /* Full width divider for mobile */
        .mobile-divider {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            width: 100vw;
            height: 120px;
            pointer-events: none;
            z-index: 0;
        }

        @media (min-width: 768px) {
            .mobile-divider {
                position: absolute;
                width: 100%;
                height: 160px;
            }
        }

        /* Prevent scrolling issues on mobile */
        html,
        body {
            height: 100%;
            overflow: hidden;
            position: fixed;
            width: 100%;
        }

        /* Safe area handling for newer phones */
        @supports (padding: max(0px)) {
            .safe-area-bottom {
                padding-bottom: max(1rem, env(safe-area-inset-bottom));
            }
        }
    </style>
</head>

<body class="bg-brand-red flex items-center justify-center min-h-screen text-center overflow-hidden">

    <!-- Custom Message Box -->
    <div id="messageBox" class="message-box"></div>

    <!-- Container -->
    <div class="w-full max-w-sm relative h-full z-10 container-mobile flex flex-col">
        <!-- Onboarding Screens with Illustrations -->
        <div class="flex-1 flex flex-col justify-center px-4">
            <div id="screen1" class="screen active flex-col items-center space-y-4 md:space-y-6">
                <!-- Font Awesome Icon for Discover Restaurants -->
                <div class="svg-container">
                    <i class="fas fa-map-marker-alt"></i>
                </div>
                <h2 class="text-responsive text-xl sm:text-2xl font-bold text-white">Discover Restaurants</h2>
                <p class="description-responsive text-sm text-white/80 max-w-xs px-2">Find local gems and popular eateries near you.</p>
            </div>

            <div id="screen2" class="screen flex-col items-center space-y-4 md:space-y-6">
                <!-- Font Awesome Icon for Explore Menus -->
                <div class="svg-container">
                    <i class="fas fa-utensils"></i>
                </div>
                <h2 class="text-responsive text-xl sm:text-2xl font-bold text-white">Explore Menus</h2>
                <p class="description-responsive text-sm text-white/80 max-w-xs px-2">Browse menus, dishes, and new food trends effortlessly.</p>
            </div>

            <div id="screen3" class="screen flex-col items-center space-y-4 md:space-y-6">
                <!-- Font Awesome Icon for Trending This Week -->
                <div class="svg-container">
                    <i class="fas fa-fire"></i>
                </div>
                <h2 class="text-responsive text-xl sm:text-2xl font-bold text-white">Trending This Week</h2>
                <p class="description-responsive text-sm text-white/80 max-w-xs px-2">Stay updated with the hottest food and trending stores.</p>
            </div>

            <div id="screen4" class="screen flex-col items-center space-y-4 md:space-y-6">
                <!-- Font Awesome Icon for Community Reviews -->
                <div class="svg-container">
                    <i class="fas fa-users"></i>
                </div>
                <h2 class="text-responsive text-xl sm:text-2xl font-bold text-white">Community Reviews</h2>
                <p class="description-responsive text-sm text-white/80 max-w-xs px-2">Get trusted reviews and share your foodie experiences.</p>
            </div>

            <div id="screen5" class="screen flex-col items-center space-y-4 md:space-y-6">
                <div class="svg-container">
                    <!-- Font Awesome Icon for Join SwadSangam -->
                    <i class="fas fa-user-plus"></i>
                </div>
                <h2 class="text-responsive text-xl sm:text-2xl font-bold text-white">Join SwadSangam</h2>
                <p class="description-responsive text-sm text-white/80 max-w-xs px-2">Create your profile and connect with foodies & restaurants.</p>
            </div>
        </div>

        <!-- Controls -->
        <div class="controls-mobile safe-area-bottom w-full flex justify-between items-center p-4 z-10">
            <button id="skipBtn" class="touch-button text-white text-sm opacity-80 hover:opacity-100 transition-opacity px-2 py-2">Skip</button>

            <!-- Pagination Dots -->
            <div id="pagination" class="flex space-x-2">
                <!-- Dots will be generated by JS -->
            </div>

            <button id="nextBtn" class="touch-button px-6 py-3 rounded-full bg-white text-brand-red font-semibold shadow hover:scale-105 transition-transform text-sm md:text-base">Next</button>
        </div>
    </div>

    <!-- Wavy Divider - Full Width Mobile -->
    <div class="mobile-divider">
        <svg class="w-full h-full" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320" preserveAspectRatio="none">
            <path fill="white" fill-opacity="0.1" d="M0,160L48,176C96,192,192,224,288,213.3C384,203,480,149,576,133.3C672,117,768,139,864,165.3C960,192,1056,224,1152,218.7C1248,213,1344,171,1392,149.3L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
        </svg>
    </div>

    <script>
        const screens = ["screen1", "screen2", "screen3", "screen4", "screen5"];
        let currentIndex = 0;
        const nextBtn = document.getElementById('nextBtn');
        const skipBtn = document.getElementById('skipBtn');
        const paginationContainer = document.getElementById('pagination');

        // Function to show a custom message
        function showMessage(message) {
            const messageBox = document.getElementById('messageBox');
            messageBox.textContent = message;
            messageBox.style.top = '20px';
            setTimeout(() => {
                messageBox.style.top = '-100px';
            }, 3000);
        }

        // Function to create pagination dots
        function createPaginationDots() {
            paginationContainer.innerHTML = ''; // Clear existing dots
            screens.forEach((_, index) => {
                const dot = document.createElement('div');
                dot.classList.add('w-2', 'h-2', 'rounded-full', 'transition-all', 'duration-300');
                if (index === currentIndex) {
                    dot.classList.add('bg-brand-yellow', 'w-4');
                } else {
                    dot.classList.add('bg-white/50');
                }
                paginationContainer.appendChild(dot);
            });
        }

        function showScreen(index) {
            screens.forEach((id, i) => {
                const screenElement = document.getElementById(id);
                if (i === index) {
                    screenElement.classList.add('active');
                } else {
                    screenElement.classList.remove('active');
                }
            });
            // Update button text on the last screen
            if (index === screens.length - 1) {
                nextBtn.textContent = 'Finish';
            } else {
                nextBtn.textContent = 'Next';
            }
            createPaginationDots();
        }

        // Enhanced touch event handling for mobile
        function addTouchSupport(element, callback) {
            let touchStartTime = 0;

            element.addEventListener('touchstart', (e) => {
                touchStartTime = Date.now();
                element.style.transform = 'scale(0.95)';
            }, {
                passive: true
            });

            element.addEventListener('touchend', (e) => {
                const touchEndTime = Date.now();
                if (touchEndTime - touchStartTime < 500) { // Prevent accidental long presses
                    e.preventDefault();
                    callback();
                }
                element.style.transform = 'scale(1)';
            }, {
                passive: false
            });

            element.addEventListener('touchcancel', () => {
                element.style.transform = 'scale(1)';
            });

            // Keep click for desktop
            element.addEventListener('click', callback);
        }

        addTouchSupport(nextBtn, () => {
            currentIndex++;
            if (currentIndex < screens.length) {
                showScreen(currentIndex);
            } else {
                showMessage('Onboarding finished! Redirecting to home page...');
                // Redirect to homepage after a short delay
                setTimeout(() => {
                    window.location.href = 'pages/web/home.php';
                }, 1500);
            }
        });

        addTouchSupport(skipBtn, () => {
            showMessage('Skipped! Redirecting to home page...');
            // Redirect to homepage after a short delay
            setTimeout(() => {
                window.location.href = 'pages/web/home.php';
            }, 1500);
        });

        // Initialize the first screen and pagination
        showScreen(currentIndex);
    </script>

</body>

</html>