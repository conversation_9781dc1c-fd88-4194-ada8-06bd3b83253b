<!-- Navigation Bar -->
<nav class="bg-white shadow-lg sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
            <!-- Logo -->
            <div class="flex items-center">
                <a href="home.php" class="flex items-center">
                    <div class="bg-brand-red text-white w-10 h-10 rounded-lg flex items-center justify-center mr-3">
                        <span class="font-bold text-lg">SS</span>
                    </div>
                    <span class="text-2xl font-bold text-brand-dark">
                        Swad<span class="text-brand-red">Sangam</span>
                    </span>
                </a>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden md:flex items-center space-x-8">
                <a href="home.php" class="text-brand-dark hover:text-brand-red transition-colors font-medium">
                    <i class="fas fa-home mr-2"></i>Home
                </a>
                <a href="#restaurants" class="text-brand-dark hover:text-brand-red transition-colors font-medium">
                    <i class="fas fa-utensils mr-2"></i>Restaurants
                </a>
                <a href="#trending" class="text-brand-dark hover:text-brand-red transition-colors font-medium">
                    <i class="fas fa-fire mr-2"></i>Trending
                </a>
                <a href="#" class="text-brand-dark hover:text-brand-red transition-colors font-medium">
                    <i class="fas fa-users mr-2"></i>Community
                </a>
                <a href="#" class="text-brand-dark hover:text-brand-red transition-colors font-medium">
                    <i class="fas fa-percent mr-2"></i>Offers
                </a>
            </div>

            <!-- Desktop User Actions -->
            <div class="hidden md:flex items-center space-x-4">
                <!-- Search Button -->
                <button class="text-gray-600 hover:text-brand-red transition-colors" onclick="toggleSearch()">
                    <i class="fas fa-search text-xl"></i>
                </button>

                <!-- Login Button -->
                <a href="../auth/login.html" class="bg-brand-red text-white px-6 py-2 rounded-full hover:bg-red-600 transition-colors font-medium">
                    <i class="fas fa-user mr-2"></i>Login
                </a>
            </div>

            <!-- Mobile Menu Button -->
            <div class="md:hidden flex items-center">
                <button class="text-gray-600 hover:text-brand-red transition-colors mr-4" onclick="toggleSearch()">
                    <i class="fas fa-search text-xl"></i>
                </button>
                <button class="mobile-menu-button text-gray-600 hover:text-brand-red focus:outline-none" onclick="toggleMobileMenu()">
                    <i class="fas fa-bars text-2xl" id="menu-icon"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile Search Bar -->
    <div id="mobile-search" class="hidden bg-gray-50 border-t px-4 py-3">
        <div class="relative">
            <input type="text"
                placeholder="Search restaurants, cuisines..."
                class="w-full px-4 py-2 pr-10 border border-gray-300 rounded-full focus:outline-none focus:border-brand-red">
            <button class="absolute right-3 top-2 text-gray-500">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>

    <!-- Mobile Navigation Menu -->
    <div id="mobile-menu" class="hidden md:hidden bg-white border-t">
        <div class="px-4 pt-2 pb-4 space-y-2">
            <a href="home.php" class="block px-3 py-2 text-brand-dark hover:bg-brand-bg hover:text-brand-red transition-colors rounded-lg font-medium">
                <i class="fas fa-home mr-3 w-5"></i>Home
            </a>
            <a href="#restaurants" class="block px-3 py-2 text-brand-dark hover:bg-brand-bg hover:text-brand-red transition-colors rounded-lg font-medium" onclick="closeMobileMenu()">
                <i class="fas fa-utensils mr-3 w-5"></i>Restaurants
            </a>
            <a href="#trending" class="block px-3 py-2 text-brand-dark hover:bg-brand-bg hover:text-brand-red transition-colors rounded-lg font-medium" onclick="closeMobileMenu()">
                <i class="fas fa-fire mr-3 w-5"></i>Trending
            </a>
            <a href="#" class="block px-3 py-2 text-brand-dark hover:bg-brand-bg hover:text-brand-red transition-colors rounded-lg font-medium">
                <i class="fas fa-users mr-3 w-5"></i>Community
            </a>
            <a href="#" class="block px-3 py-2 text-brand-dark hover:bg-brand-bg hover:text-brand-red transition-colors rounded-lg font-medium">
                <i class="fas fa-percent mr-3 w-5"></i>Offers
            </a>
            <hr class="my-3">
            <a href="../auth/login.html" class="block px-3 py-2 bg-brand-red text-white rounded-lg font-medium text-center hover:bg-red-600 transition-colors">
                <i class="fas fa-user mr-2"></i>Login / Sign Up
            </a>
        </div>
    </div>
</nav>

<script>
    function toggleMobileMenu() {
        const mobileMenu = document.getElementById('mobile-menu');
        const menuIcon = document.getElementById('menu-icon');

        if (mobileMenu.classList.contains('hidden')) {
            mobileMenu.classList.remove('hidden');
            menuIcon.classList.remove('fa-bars');
            menuIcon.classList.add('fa-times');
        } else {
            mobileMenu.classList.add('hidden');
            menuIcon.classList.remove('fa-times');
            menuIcon.classList.add('fa-bars');
        }
    }

    function closeMobileMenu() {
        const mobileMenu = document.getElementById('mobile-menu');
        const menuIcon = document.getElementById('menu-icon');

        mobileMenu.classList.add('hidden');
        menuIcon.classList.remove('fa-times');
        menuIcon.classList.add('fa-bars');
    }

    function toggleSearch() {
        const searchBar = document.getElementById('mobile-search');
        searchBar.classList.toggle('hidden');
    }

    // Close mobile menu when clicking outside
    document.addEventListener('click', function(event) {
        const mobileMenu = document.getElementById('mobile-menu');
        const menuButton = document.querySelector('.mobile-menu-button');

        if (!menuButton.contains(event.target) && !mobileMenu.contains(event.target)) {
            closeMobileMenu();
        }
    });

    // Close mobile menu on window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 768) {
            closeMobileMenu();
        }
    });
</script>