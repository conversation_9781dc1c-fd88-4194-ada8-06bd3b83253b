<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="theme-color" content="#E23744">
    <title>SwadSangam - Food Discovery App</title>
    
    <!-- Include Header -->
    <?php include 'includes/head.php'; ?>
</head>
<body class="bg-gray-50 font-inter overflow-x-hidden">
    <!-- Mobile App Interface -->
    <main class="pb-20">
        <!-- App Header -->
        <header class="bg-white px-4 py-3 shadow-sm">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="bg-brand-red text-white w-8 h-8 rounded-lg flex items-center justify-center mr-3">
                        <span class="font-bold text-sm">SS</span>
                    </div>
                    <div>
                        <p class="text-gray-600 text-xs">Welcome back!</p>
                        <h1 class="text-brand-dark font-semibold text-lg">SwadSangam</h1>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="relative">
                        <i class="fas fa-bell text-gray-600 text-xl"></i>
                        <span class="absolute -top-1 -right-1 bg-brand-red text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">3</span>
                    </button>
                    <button class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-gray-600"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Search Section -->
        <section class="px-4 py-4 bg-white">
            <div class="relative">
                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                <input type="text" 
                       placeholder="Search restaurants, dishes..." 
                       class="w-full pl-10 pr-4 py-3 bg-gray-50 rounded-xl border-0 focus:outline-none focus:bg-white focus:shadow-md transition-all">
                <button class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-brand-red text-white p-2 rounded-lg">
                    <i class="fas fa-sliders-h text-sm"></i>
                </button>
            </div>
        </section>

        <!-- Quick Actions -->
        <section class="px-4 py-4">
            <div class="grid grid-cols-4 gap-4">
                <button class="flex flex-col items-center p-4 bg-white rounded-xl shadow-sm">
                    <div class="bg-red-100 p-3 rounded-full mb-2">
                        <i class="fas fa-utensils text-brand-red"></i>
                    </div>
                    <span class="text-xs text-gray-600 text-center">Restaurants</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-white rounded-xl shadow-sm">
                    <div class="bg-orange-100 p-3 rounded-full mb-2">
                        <i class="fas fa-fire text-orange-500"></i>
                    </div>
                    <span class="text-xs text-gray-600 text-center">Trending</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-white rounded-xl shadow-sm">
                    <div class="bg-green-100 p-3 rounded-full mb-2">
                        <i class="fas fa-percent text-green-500"></i>
                    </div>
                    <span class="text-xs text-gray-600 text-center">Offers</span>
                </button>
                <button class="flex flex-col items-center p-4 bg-white rounded-xl shadow-sm">
                    <div class="bg-purple-100 p-3 rounded-full mb-2">
                        <i class="fas fa-star text-purple-500"></i>
                    </div>
                    <span class="text-xs text-gray-600 text-center">Reviews</span>
                </button>
            </div>
        </section>

        <!-- Promotional Banner -->
        <section class="px-4 py-4">
            <div class="bg-gradient-to-r from-brand-red to-pink-500 rounded-2xl p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="font-bold text-lg mb-1">Free Delivery</h3>
                        <p class="text-sm opacity-90">On orders above ₹299</p>
                        <button class="bg-white text-brand-red px-4 py-2 rounded-full text-sm font-semibold mt-3">
                            Order Now
                        </button>
                    </div>
                    <div class="text-6xl opacity-20">
                        <i class="fas fa-motorcycle"></i>
                    </div>
                </div>
            </div>
        </section>

        <!-- Popular Restaurants Section -->
        <section class="px-4 py-4">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-bold text-brand-dark">Popular Near You</h2>
                <button class="text-brand-red text-sm font-medium">View All</button>
            </div>
            
            <div class="space-y-4">
                <!-- Restaurant Card 1 -->
                <div class="bg-white rounded-xl p-4 shadow-sm flex items-center space-x-4">
                    <div class="w-16 h-16 bg-gradient-to-r from-brand-red to-pink-500 rounded-xl flex items-center justify-center">
                        <i class="fas fa-utensils text-2xl text-white"></i>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-1">
                            <h3 class="font-semibold text-brand-dark">Spice Garden</h3>
                            <div class="flex items-center">
                                <i class="fas fa-star text-brand-yellow text-xs"></i>
                                <span class="ml-1 text-sm text-gray-600">4.5</span>
                            </div>
                        </div>
                        <p class="text-gray-500 text-sm mb-2">North Indian • 25 min</p>
                        <div class="flex items-center justify-between">
                            <span class="text-brand-red font-medium text-sm">₹500 for two</span>
                            <span class="bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs">Open</span>
                        </div>
                    </div>
                </div>
                
                <!-- Restaurant Card 2 -->
                <div class="bg-white rounded-xl p-4 shadow-sm flex items-center space-x-4">
                    <div class="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
                        <i class="fas fa-pizza-slice text-2xl text-white"></i>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-1">
                            <h3 class="font-semibold text-brand-dark">Pizza Corner</h3>
                            <div class="flex items-center">
                                <i class="fas fa-star text-brand-yellow text-xs"></i>
                                <span class="ml-1 text-sm text-gray-600">4.2</span>
                            </div>
                        </div>
                        <p class="text-gray-500 text-sm mb-2">Italian • 15 min</p>
                        <div class="flex items-center justify-between">
                            <span class="text-brand-red font-medium text-sm">₹300 for two</span>
                            <span class="bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs">Open</span>
                        </div>
                    </div>
                </div>
                
                <!-- Restaurant Card 3 -->
                <div class="bg-white rounded-xl p-4 shadow-sm flex items-center space-x-4">
                    <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-teal-500 rounded-xl flex items-center justify-center">
                        <i class="fas fa-leaf text-2xl text-white"></i>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-1">
                            <h3 class="font-semibold text-brand-dark">Green Bistro</h3>
                            <div class="flex items-center">
                                <i class="fas fa-star text-brand-yellow text-xs"></i>
                                <span class="ml-1 text-sm text-gray-600">4.7</span>
                            </div>
                        </div>
                        <p class="text-gray-500 text-sm mb-2">Healthy • 30 min</p>
                        <div class="flex items-center justify-between">
                            <span class="text-brand-red font-medium text-sm">₹400 for two</span>
                            <span class="bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs">Open</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Trending & Categories -->
        <section class="px-4 py-4">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-bold text-brand-dark">What's Trending</h2>
                <button class="text-brand-red text-sm font-medium">See More</button>
            </div>
            
            <div class="flex space-x-3 overflow-x-auto pb-2">
                <!-- Trending Card 1 -->
                <div class="min-w-[280px] bg-white rounded-xl p-4 shadow-sm">
                    <div class="flex items-center mb-3">
                        <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-fire text-orange-500"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-brand-dark text-sm">Butter Chicken</h3>
                            <p class="text-gray-500 text-xs">Most ordered this week</p>
                        </div>
                    </div>
                    <p class="text-gray-600 text-sm">Creamy, flavorful dish loved by food enthusiasts</p>
                </div>
                
                <!-- Trending Card 2 -->
                <div class="min-w-[280px] bg-white rounded-xl p-4 shadow-sm">
                    <div class="flex items-center mb-3">
                        <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-star text-green-500"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-brand-dark text-sm">New Opening</h3>
                            <p class="text-gray-500 text-xs">Green Bistro</p>
                        </div>
                    </div>
                    <p class="text-gray-600 text-sm">Fresh, organic meals in a cozy atmosphere</p>
                </div>
            </div>
        </section>
    </main>

    <!-- Mobile Bottom Navigation -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2">
        <div class="flex justify-around items-center">
            <button class="flex flex-col items-center py-2 text-brand-red">
                <i class="fas fa-home text-lg mb-1"></i>
                <span class="text-xs">Home</span>
            </button>
            <button class="flex flex-col items-center py-2 text-gray-400">
                <i class="fas fa-search text-lg mb-1"></i>
                <span class="text-xs">Search</span>
            </button>
            <button class="flex flex-col items-center py-2 text-gray-400">
                <i class="fas fa-heart text-lg mb-1"></i>
                <span class="text-xs">Favorites</span>
            </button>
            <button class="flex flex-col items-center py-2 text-gray-400">
                <i class="fas fa-shopping-bag text-lg mb-1"></i>
                <span class="text-xs">Orders</span>
            </button>
            <button class="flex flex-col items-center py-2 text-gray-400">
                <i class="fas fa-user text-lg mb-1"></i>
                <span class="text-xs">Profile</span>
            </button>
        </div>
    </nav>

    <script>
    // Mobile app interactions
    document.addEventListener('DOMContentLoaded', function() {
        // Add touch feedback to buttons
        const buttons = document.querySelectorAll('button');
        buttons.forEach(button => {
            button.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.95)';
            });
            
            button.addEventListener('touchend', function() {
                this.style.transform = 'scale(1)';
            });
            
            button.addEventListener('touchcancel', function() {
                this.style.transform = 'scale(1)';
            });
        });

        // Bottom navigation active state
        const navButtons = document.querySelectorAll('nav button');
        navButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active state from all buttons
                navButtons.forEach(btn => {
                    btn.classList.remove('text-brand-red');
                    btn.classList.add('text-gray-400');
                });
                
                // Add active state to clicked button
                this.classList.remove('text-gray-400');
                this.classList.add('text-brand-red');
            });
        });

        // Search input focus effects
        const searchInput = document.querySelector('input[type="text"]');
        searchInput.addEventListener('focus', function() {
            this.parentElement.classList.add('shadow-md');
        });
        
        searchInput.addEventListener('blur', function() {
            this.parentElement.classList.remove('shadow-md');
        });
    });
    </script>
</body>
</html>